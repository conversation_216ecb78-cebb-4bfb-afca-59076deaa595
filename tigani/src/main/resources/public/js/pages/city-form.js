const CityForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitCity();
        _componentInputFormatting();
    };

    // Validation config
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Setup form validation
        $('.form-validate-jquery').validate({
            ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
            errorClass: 'validation-invalid-label',
            successClass: 'validation-valid-label',
            validClass: 'validation-valid-label',
            errorElement: 'span',
            errorPlacement: function(error, element) {
                error.addClass('invalid-feedback');
                error.insertAfter(element);
            },
            highlight: function(element, errorClass) {
                $(element).removeClass('is-valid').addClass('is-invalid');
            },
            unhighlight: function(element, errorClass) {
                $(element).removeClass('is-invalid').addClass('is-valid');
            },

            rules: {
                name: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                codiceIstat: {
                    required: true,
                    minlength: 6,
                    maxlength: 6,
                    digits: true
                },
                codiceBelfiore: {
                    required: true,
                    minlength: 4,
                    maxlength: 4
                },
                cap: {
                    required: true,
                    minlength: 5,
                    maxlength: 5,
                    digits: true
                },
                provinceCode: {
                    required: true,
                    minlength: 2,
                    maxlength: 2
                },
                province: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                region: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                countryCode: {
                    required: true,
                    minlength: 2,
                    maxlength: 3
                }
            },

            messages: {
                name: {
                    required: "Il nome della città è obbligatorio",
                    minlength: "Il nome deve essere di almeno 2 caratteri",
                    maxlength: "Il nome non può superare i 100 caratteri"
                },
                codiceIstat: {
                    required: "Il codice ISTAT è obbligatorio",
                    minlength: "Il codice ISTAT deve essere di 6 cifre",
                    maxlength: "Il codice ISTAT deve essere di 6 cifre",
                    digits: "Il codice ISTAT deve contenere solo numeri"
                },
                codiceBelfiore: {
                    required: "Il codice Belfiore è obbligatorio",
                    minlength: "Il codice Belfiore deve essere di 4 caratteri",
                    maxlength: "Il codice Belfiore deve essere di 4 caratteri"
                },
                cap: {
                    required: "Il CAP è obbligatorio",
                    minlength: "Il CAP deve essere di 5 cifre",
                    maxlength: "Il CAP deve essere di 5 cifre",
                    digits: "Il CAP deve contenere solo numeri"
                },
                provinceCode: {
                    required: "Il codice provincia è obbligatorio",
                    minlength: "Il codice provincia deve essere di 2 caratteri",
                    maxlength: "Il codice provincia deve essere di 2 caratteri"
                },
                province: {
                    required: "Il nome della provincia è obbligatorio",
                    minlength: "Il nome della provincia deve essere di almeno 2 caratteri",
                    maxlength: "Il nome della provincia non può superare i 100 caratteri"
                },
                region: {
                    required: "Il nome della regione è obbligatorio",
                    minlength: "Il nome della regione deve essere di almeno 2 caratteri",
                    maxlength: "Il nome della regione non può superare i 100 caratteri"
                },
                countryCode: {
                    required: "Il codice paese è obbligatorio",
                    minlength: "Il codice paese deve essere di almeno 2 caratteri",
                    maxlength: "Il codice paese non può superare i 3 caratteri"
                }
            }
        });
    };

    // Maxlength indicator
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic maxlength
        $('.maxlength').maxlength({
            alwaysShow: true,
            threshold: 10,
            warningClass: "badge bg-warning",
            limitReachedClass: "badge bg-danger"
        });
    };

    // Delete button functionality
    const _componentDeleteButton = function () {
        const deleteButton = document.getElementById('delete-city-btn');
        if (deleteButton) {
            deleteButton.addEventListener('click', function (e) {
                e.preventDefault();
                
                const cityId = this.getAttribute('data-city-id');
                if (!cityId) {
                    console.error('City ID not found for deletion');
                    return;
                }

                if (confirm('Sei sicuro di voler eliminare definitivamente questa città? Questa azione non può essere annullata.')) {
                    const formData = new FormData();
                    formData.append('cityIds', cityId);
                    formData.append('operation', 'delete');

                    $.ajax({
                        url: appRoutes.get("BE_CITY_OPERATE"),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (response) {
                            showToast('Città eliminata correttamente.', 'success');
                            
                            // Close offcanvas and reload table
                            const offcanvasElement = document.querySelector('[data-hs-overlay].hs-overlay-open');
                            if (offcanvasElement) {
                                HSOverlay.close(offcanvasElement);
                            }
                            
                            // Reload the cities table if it exists
                            if (typeof CityCollection !== 'undefined' && CityCollection.reloadTable) {
                                CityCollection.reloadTable();
                            }
                        },
                        error: function (error) {
                            showToast(error.responseText || 'Errore durante l\'eliminazione', 'error');
                            console.error('Error during city deletion', error);
                        }
                    });
                }
            });
        }
    };

    // Permission-based UI adjustments
    const _componentPermissionChecks = function () {
        // This function can be extended to handle permission-based UI changes
        // For now, permissions are handled server-side in the template
    };

    // Submit city form
    const _componentSubmitCity = function () {
        const form = document.getElementById('city-edit-offcanvas');
        if (form) {
            form.addEventListener('submit', function (e) {
                e.preventDefault();

                // Validate form first
                if (!$(form).valid()) {
                    return false;
                }

                const formData = new FormData(form);
                const submitButton = form.querySelector('button[type="submit"]');
                const originalText = submitButton ? submitButton.innerHTML : '';

                // Disable submit button and show loading state
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Salvando...';
                }

                $.ajax({
                    url: form.action,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        showToast('Città salvata correttamente.', 'success');
                        
                        // Close offcanvas
                        const offcanvasElement = document.querySelector('[data-hs-overlay].hs-overlay-open');
                        if (offcanvasElement) {
                            HSOverlay.close(offcanvasElement);
                        }
                        
                        // Reload the cities table if it exists
                        if (typeof CityCollection !== 'undefined' && CityCollection.reloadTable) {
                            CityCollection.reloadTable();
                        }
                    },
                    error: function (xhr, status, error) {
                        let errorMessage = 'Errore durante il salvataggio della città.';
                        
                        if (xhr.responseText) {
                            try {
                                const errorResponse = JSON.parse(xhr.responseText);
                                if (errorResponse.message) {
                                    errorMessage = errorResponse.message;
                                }
                            } catch (e) {
                                errorMessage = xhr.responseText;
                            }
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during city save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    },
                    complete: function () {
                        // Re-enable submit button and restore original text
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.innerHTML = originalText;
                        }
                    }
                });
            });
        }
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Province code field - uppercase transformation
        $('input[name="provinceCode"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Country code field - uppercase transformation
        $('input[name="countryCode"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Codice Belfiore field - uppercase transformation
        $('input[name="codiceBelfiore"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Name field - trim whitespace and capitalize first letter
        $('input[name="name"]').on('blur', function() {
            let value = this.value.trim();
            if (value) {
                // Capitalize first letter of each word
                value = value.replace(/\b\w/g, l => l.toUpperCase());
                this.value = value;
            }
        });

        // Province and region fields - trim whitespace and capitalize first letter
        $('input[name="province"], input[name="region"]').on('blur', function() {
            let value = this.value.trim();
            if (value) {
                // Capitalize first letter of each word
                value = value.replace(/\b\w/g, l => l.toUpperCase());
                this.value = value;
            }
        });

        // Numeric fields - ensure only digits
        $('input[name="codiceIstat"], input[name="cap"]').on('input', function() {
            this.value = this.value.replace(/\D/g, '');
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    CityForm.init();
});
