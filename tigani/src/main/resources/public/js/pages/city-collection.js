// Global variables for table management
const CityCollection = function () {

    // Datatable
    const _componentDatatable = function () {
        // Initialize Preline UI DataTable with JavaScript configuration
        const datatableConfig = {
            paging: true,
            pageLength: 10,
            searching: true,
            ordering: true,
            info: true,
            lengthChange: true,
            scrollCollapse: true,
            ajax: {
                url: appRoutes.get("BE_CITY_DATA"),
                type: 'GET'
            },
            select: {
                style: 'multi',
                selector: 'td:select-checkbox'
            },
            responsive: {
                details: {
                    type: 'column',
                    target: -1,
                }
            },
            columns: [
                {
                    data: 0,
                    orderable: false,
                    searchable: false,
                    className: 'select-checkbox',
                    render: function (data, type, row) {
                        return '<input type="checkbox" class="border-gray-300 rounded-sm text-blue-600 checked:border-blue-500 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800 cursor-pointer" data-city-id="' + data + '">';
                    }
                },
                {
                    data: 1,
                    orderable: true,
                    searchable: true,
                    render: function (data, type, row) {
                        return data;
                    }
                },
                {
                    data: 2,
                    orderable: true,
                    searchable: true
                },
                {
                    data: 3,
                    orderable: true,
                    searchable: true
                },
                {
                    data: 4,
                    orderable: true,
                    searchable: true
                },
                {
                    data: 5,
                    orderable: true,
                    searchable: true
                },
                {
                    data: 6,
                    orderable: true,
                    searchable: true
                },
                {
                    data: 7,
                    orderable: true,
                    searchable: false
                },
                {
                    data: 8,
                    orderable: true,
                    searchable: false
                },
                {
                    data: 9,
                    orderable: false,
                    searchable: false,
                    render: function (data, type, row) {
                        let actions = '<div class="flex items-center gap-x-2">';
                        
                        // Edit action
                        actions += '<button type="button" class="inline-flex items-center gap-x-1 py-1.5 px-2.5 text-xs font-medium rounded-md border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none" onclick="window._editSingleRow(\'' + row[0] + '\'); return false;">';
                        actions += '<svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/></svg>';
                        actions += 'Modifica</button>';
                        
                        // Archive action
                        actions += '<button type="button" class="inline-flex items-center gap-x-1 py-1.5 px-2.5 text-xs font-medium rounded-md border border-gray-200 bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" onclick="window._archiveSingleRow(\'' + row[0] + '\'); return false;">';
                        actions += '<svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="5" x="2" y="3" rx="1"/><path d="M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8"/><path d="M10 12h4"/></svg>';
                        actions += 'Archivia</button>';
                        
                        // Delete action
                        actions += '<button type="button" class="inline-flex items-center gap-x-1 py-1.5 px-2.5 text-xs font-medium rounded-md border border-transparent bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:bg-red-700 disabled:opacity-50 disabled:pointer-events-none" onclick="window._deleteSingleRow(\'' + row[0] + '\'); return false;">';
                        actions += '<svg class="shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/><line x1="10" x2="10" y1="11" y2="17"/><line x1="14" x2="14" y1="11" y2="17"/></svg>';
                        actions += 'Elimina</button>';
                        
                        actions += '</div>';
                        return actions;
                    }
                }
            ]
        };

        const tableEl = document.getElementById('city-datatable-container');
        const hsDataTable = new HSDataTable(tableEl, datatableConfig);

        // On draw initialize HS components and setup checkbox handlers
        hsDataTable.dataTable.on('draw', function() {
            HSStaticMethods.autoInit();
            _setupCheckboxHandlers();
        });

        window.citiesDataTable = hsDataTable;

        const buttons = document.querySelectorAll('#hs-dropdown-datatable-with-export .hs-dropdown-menu button');
        buttons.forEach((btn) => {
            const type = btn.getAttribute('data-hs-datatable-action-type');

            btn.addEventListener('click', () => hsDataTable.dataTable.button(`.buttons-${type}`).trigger());
        });

        // Initial setup of checkbox handlers
        _setupCheckboxHandlers();
    };

    // Setup checkbox handlers for bulk actions
    const _setupCheckboxHandlers = function() {
        // Master checkbox handler
        const masterCheckbox = document.getElementById('hs-table-search-checkbox-all');
        if (masterCheckbox) {
            masterCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                const checkboxes = document.querySelectorAll('input[data-city-id]');
                
                checkboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                
                _updateBulkActionsVisibility();
            });
        }

        // Individual checkbox handlers
        document.addEventListener('change', function(e) {
            if (e.target.matches('input[data-city-id]')) {
                _updateBulkActionsVisibility();
                
                // Update master checkbox state
                const allCheckboxes = document.querySelectorAll('input[data-city-id]');
                const checkedCheckboxes = document.querySelectorAll('input[data-city-id]:checked');
                
                if (masterCheckbox) {
                    masterCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
                    masterCheckbox.checked = checkedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0;
                }
            }
        });
    };

    // Update bulk actions visibility based on selection
    const _updateBulkActionsVisibility = function() {
        const selectedRows = _getSelectedRows();
        const bulkContainer = document.getElementById('bulk-actions-container');
        const selectedCount = document.getElementById('selected-count');
        
        if (bulkContainer && selectedCount) {
            if (selectedRows.length > 0) {
                selectedCount.textContent = selectedRows.length;
                bulkContainer.classList.remove('hidden');
            } else {
                bulkContainer.classList.add('hidden');
            }
        }
    };

    // Get selected row IDs
    const _getSelectedRows = function() {
        const selectedCheckboxes = document.querySelectorAll('input[data-city-id]:checked');
        return Array.from(selectedCheckboxes).map(checkbox => checkbox.getAttribute('data-city-id'));
    };

    // Clear all selections
    const _clearSelection = function() {
        const checkboxes = document.querySelectorAll('input[data-city-id], #hs-table-search-checkbox-all');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        });
        _updateBulkActionsVisibility();
    };

    // Add new city functionality
    const _componentAddCity = function () {
        const createButton = document.getElementById('create-city-btn');
        if (createButton) {
            createButton.addEventListener('click', function (e) {
                e.preventDefault();
                
                try {
                    console.log('Opening create city form...');

                    const offcanvas = createDynamicOffcanvas({
                        title: 'Nuova Città',
                        url: appRoutes.get('BE_CITY_FORM'),
                        entity: 'city',
                        onContentLoaded: function(offcanvasElement, contentContainer) {
                            try {
                                // Initialize city form components after content is loaded
                                if (typeof CityForm !== 'undefined' && CityForm.init) {
                                    CityForm.init();
                                }
                            } catch (initError) {
                                console.error('Error initializing form:', initError);
                                showToast('Errore nell\'inizializzazione del modulo', 'error');
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error creating offcanvas:', error);
                    showToast('Errore nell\'apertura del modulo', 'error');
                }
            });
        } else {
            console.warn('Create city button not found');
        }
    }

    // Edit city functionality
    const _componentEditCity = function () {
        // Handle click events on city name links
        document.addEventListener('click', function(e) {
            if (e.target.matches('a[cityId]')) {
                e.preventDefault();
                
                const cityId = e.target.getAttribute('cityId');
                const cityName = e.target.textContent.trim();
                
                _editSingleRow(cityId, cityName);
            }
        });
    }

    // Edit single row function
    const _editSingleRow = function(cityId, cityName) {
        if (!cityId) {
            console.error('City ID is required for editing');
            return;
        }

        try {
            console.log('Opening edit city form for ID:', cityId);

            const offcanvas = createDynamicOffcanvas({
                title: 'Modifica Città: ' + (cityName || 'Sconosciuto'),
                url: appRoutes.get('BE_CITY_FORM') + '?cityId=' + encodeURIComponent(cityId),
                entity: 'city',
                entityId: cityId,
                onContentLoaded: function(offcanvasElement, contentContainer) {
                    try {
                        // Initialize city form components after content is loaded
                        if (typeof CityForm !== 'undefined' && CityForm.init) {
                            CityForm.init();
                        }
                    } catch (initError) {
                        console.error('Error initializing form:', initError);
                        showToast('Errore nell\'inizializzazione del modulo', 'error');
                    }
                }
            });
        } catch (error) {
            console.error('Error opening edit form:', error);
            showToast('Errore nell\'apertura del modulo di modifica', 'error');
        }
    }

    // Archive single row
    const _archiveSingleRow = function(cityId) {
        if (!cityId) {
            console.error('City ID is required for archiving');
            return;
        }

        if (confirm('Sei sicuro di voler archiviare questa città?')) {
            _performSingleRowAction(cityId, 'archive');
        }
    };

    // Delete single row
    const _deleteSingleRow = function(cityId) {
        if (!cityId) {
            console.error('City ID is required for deletion');
            return;
        }

        if (confirm('Sei sicuro di voler eliminare definitivamente questa città? Questa azione non può essere annullata.')) {
            _performSingleRowAction(cityId, 'delete');
        }
    };

    // Archive selected rows
    const _archiveSelectedRows = function() {
        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno una città da archiviare', 'warning');
            return;
        }

        if (confirm(`Sei sicuro di voler archiviare ${selectedRows.length} città selezionate?`)) {
            _performBulkAction(selectedRows, 'archive');
        }
    };

    // Delete selected rows
    const _deleteSelectedRows = function() {
        const selectedRows = _getSelectedRows();
        if (selectedRows.length === 0) {
            showToast('Seleziona almeno una città da eliminare', 'warning');
            return;
        }

        if (confirm(`Sei sicuro di voler eliminare definitivamente ${selectedRows.length} città selezionate? Questa azione non può essere annullata.`)) {
            _performBulkAction(selectedRows, 'delete');
        }
    };

    // Perform bulk action
    const _performBulkAction = function(cityIds, operation) {
        const formData = new FormData();
        formData.append('cityIds', cityIds.join(','));
        formData.append('operation', operation);
        formData.append('fromArchived', $("#city_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_CITY_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                _clearSelection();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during city bulk operation', error);
            }
        });
    };

    // Perform single row action
    function _performSingleRowAction(cityId, operation) {
        const formData = new FormData();
        formData.append('cityIds', cityId);
        formData.append('operation', operation);
        formData.append('fromArchived', $("#city_archived:checked").length > 0);

        $.ajax({
            url: appRoutes.get("BE_CITY_OPERATE"),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                _reloadTable();
                showToast('Operazione completata correttamente.', 'success');
            },
            error: function (error) {
                showToast(error.responseText || 'Errore durante l\'operazione', 'error');
                console.error('Error during city operation', error);
            }
        });
    }

    // Reload table data
    const _reloadTable = function(showArchived) {
        if (window.citiesDataTable && window.citiesDataTable.dataTable) {
            // Update the ajax URL with archived parameter if provided
            if (typeof showArchived !== 'undefined') {
                const currentUrl = window.citiesDataTable.dataTable.ajax.url();
                const baseUrl = appRoutes.get("BE_CITY_DATA");
                const newUrl = showArchived ? baseUrl + '?archived=true' : baseUrl;
                window.citiesDataTable.dataTable.ajax.url(newUrl);
            }
            window.citiesDataTable.dataTable.ajax.reload(null, false);
        }
    };

    // Make individual action functions globally accessible
    window._editSingleRow = function(cityId, cityName) {
        _editSingleRow(cityId, cityName);
    };
    window._archiveSingleRow = _archiveSingleRow;
    window._deleteSingleRow = _deleteSingleRow;

    //
    // Return objects assigned to module
    //

    return {
        init: function () {
            _componentDatatable();
            _componentAddCity();
            _componentEditCity();
        },
        reloadTable: _reloadTable,
        archiveSelectedRows: _archiveSelectedRows,
        deleteSelectedRows: _deleteSelectedRows,
        getSelectedRows: _getSelectedRows,
        clearSelection: _clearSelection
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    CityCollection.init();
});
